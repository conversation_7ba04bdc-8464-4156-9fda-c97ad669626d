/* Skeleton Layout - Exact Gauzy Structure with Animation */

/* Smooth Shimmer Animation */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.skeleton-layout {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: var(--z-index-overlay, 1003);
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Base skeleton element with smooth shimmer */
.skeleton-element {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite ease-in-out;
  }
}

/* Header */
.skeleton-header {
  height: 60px;
  background: #2a2a2a;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 24px;
  flex-shrink: 0;
}

.skeleton-header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.skeleton-action-item {
  height: 32px;
  width: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
}

/* Main Body Layout */
.skeleton-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* Sidebar */
.skeleton-sidebar {
  width: 260px;
  background: #2a2a2a;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  position: relative;
}

/* Logo Section */
.skeleton-logo-section {
  padding: 20px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.skeleton-logo {
  height: 40px;
  width: 120px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 6px;
}

/* Navigation */
.skeleton-nav {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 20px 16px;
  flex: 1;
}

.skeleton-nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 6px;

  &.active {
    background: rgba(255, 255, 255, 0.1);
  }
}

/* User Section at Bottom of Sidebar */
.skeleton-user-section {
  position: absolute;
  bottom: 20px;
  left: 16px;
  right: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.skeleton-user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.skeleton-user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.skeleton-user-name {
  height: 14px;
  width: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.skeleton-user-role {
  height: 12px;
  width: 60px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 2px;
}

.skeleton-nav-icon {
  @extend .skeleton-element;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.skeleton-nav-text {
  @extend .skeleton-element;
  height: 16px;
  width: 120px;
  flex: 1;
}

/* Main Content */
.skeleton-main {
  flex: 1;
  background: var(--skeleton-bg, #202023);
  overflow-y: auto;
  padding: 24px;
}

/* Page Header */
.skeleton-page-header {
  margin-bottom: 32px;
}

.skeleton-page-title {
  @extend .skeleton-element;
  height: 32px;
  width: 400px;
  margin-bottom: 8px;
}

.skeleton-page-subtitle {
  @extend .skeleton-element;
  height: 16px;
  width: 250px;
}

/* Stats Grid (6 cards) */
.skeleton-stats {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  margin-bottom: 32px;
}

.skeleton-stat-card {
  background: #333333;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 120px;
}

.skeleton-stat-value {
  @extend .skeleton-element;
  height: 36px;
  width: 70%;
}

.skeleton-stat-label {
  @extend .skeleton-element;
  height: 14px;
  width: 90%;
}

/* Bottom Content (Recent Activities + Tasks) */
.skeleton-bottom-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
}

.skeleton-left-section,
.skeleton-right-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 24px;
}

.skeleton-section-title {
  @extend .skeleton-element;
  height: 20px;
  width: 150px;
  margin-bottom: 20px;
}

.skeleton-activities,
.skeleton-tasks {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.skeleton-activity-item,
.skeleton-task-item {
  @extend .skeleton-element;
  height: 60px;
  width: 100%;
}

/* Responsive Design */
@media (max-width: 1400px) {
  .skeleton-stats {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 1024px) {
  .skeleton-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .skeleton-bottom-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .skeleton-body {
    flex-direction: column;
  }

  .skeleton-sidebar {
    width: 100%;
    height: auto;
    max-height: 200px;
    padding: 16px;
  }

  .skeleton-nav {
    flex-direction: row;
    gap: 8px;
    overflow-x: auto;
  }

  .skeleton-nav-item {
    flex-shrink: 0;
    min-width: 140px;
  }

  .skeleton-stats {
    grid-template-columns: 1fr;
  }

  .skeleton-main {
    padding: 16px;
  }

  .skeleton-top-header {
    padding: 0 16px;
  }

  .skeleton-nav-tabs {
    display: none;
  }
}
